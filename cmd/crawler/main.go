package main

import (
	_36kr "36kr"
	"encoding/json"
	"fmt"
	"github.com/go-resty/resty/v2"
	"os"
	"strings"
	"time"
)

func main() {
	pageNo := 1
	result := make([]_36kr.Investor, 0)

	for pageNo <= 16 {
		resp := &_36kr.Response{}
		_, err := resty.New().R().
			SetHeader("Accept", "*/*").
			SetHeader("Accept-Language", "zh-CN,zh;q=0.9").
			SetHeader("Connection", "keep-alive").
			SetHeader("Content-Type", "application/json").
			SetHeader("Origin", "https://pitchhub.36kr.com").
			SetHeader("Referer", "https://pitchhub.36kr.com/").
			SetHeader("Sec-Fetch-Dest", "empty").
			SetHeader("Sec-Fetch-Mode", "cors").
			SetHeader("Sec-Fetch-Site", "same-site").
			SetHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36").
			SetHeader("sec-ch-ua", "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"").
			SetHeader("sec-ch-ua-mobile", "?0").
			SetHeader("sec-ch-ua-platform", "\"macOS\"").
			SetBody(_36kr.Request{
				PartnerID:      "web",
				Timestamp:      time.Now().UnixMilli(),
				PartnerVersion: "1.0.0",
				Param: _36kr.RequestParam{
					PageNo:     fmt.Sprintf("%d", pageNo),
					PageSize:   fmt.Sprintf("%d", 20),
					TypeIdList: []string{"4"},
					Keyword:    "",
					SiteId:     1,
					PlatformId: 2,
				},
			}).
			SetResult(resp).
			Post("https://gateway.36kr.com/api/pms/institution/list")
		if err != nil {
			panic(err)
		}

		if resp.Code != 0 {
			panic(resp)
		}

		result = append(result, resp.Data.InvestorList...)
		pageNo += 1
	}
	fmt.Println(result)

	extractor := NewXPathExtractor()
	for i, investor := range result {
		arr, err := extractor.ExtractWithWait(
			fmt.Sprintf("https://pitchhub.36kr.com/organization/%d", investor.ID),
			[]string{
				"/html/body/div[1]/section/section/main/div[2]/div/div/div[3]/div/div[1]/div/div[2]/div[2]/div[3]/table/tbody/tr[1]/td[1]/div/a/div[2]/div[1]",
				"/html/body/div[1]/section/section/main/div[2]/div/div/div[3]/div/div[1]/div/div[2]/div[2]/div[3]/table/tbody/tr[1]/td[3]/div",
			})
		if err != nil {
			break
		}
		split := strings.Split(arr[1], "-")
		if len(split) == 2 {
			result[i].ProjectName = split[0] + "-" + investor.ProjectName
		}
	}
	marshal, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		panic(err)
	}
	file, err := os.Create("investor.json")
	if err != nil {
		panic(err)
	}
	defer file.Close()

	_, err = file.Write(marshal)
	if err != nil {
		panic(err)
	}
}

package main

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/chromedp/chromedp"
	"log"
)

type XPathExtractor struct {
	ctx    context.Context
	cancel context.CancelFunc
}

func NewXPathExtractor() *XPathExtractor {
	opts := append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.Flag("headless", false), // 显示浏览器窗口，方便调试
		chromedp.Flag("disable-gpu", false),
		chromedp.Flag("disable-dev-shm-usage", true),
		chromedp.Flag("disable-extensions", true),
		chromedp.Flag("no-sandbox", true),
		chromedp.UserAgent("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"),
	)
	allocCtx, _ := chromedp.NewExecAllocator(context.Background(), opts...)
	ctx, cancel := chromedp.NewContext(allocCtx)
	return &XPathExtractor{
		ctx:    ctx,
		cancel: cancel,
	}
}

func (xe *XPathExtractor) Close() {
	xe.cancel()
}

func (xe *XPathExtractor) ExtractByXPath(url string, xpaths []string) error {
	log.Printf("正在访问网页: %s", url)
	err := chromedp.Run(xe.ctx,
		chromedp.Navigate(url),
		chromedp.WaitVisible(`body`, chromedp.ByQuery),
	)
	if err != nil {
		return fmt.Errorf("访问网页失败: %v", err)
	}
	log.Println("页面加载完成，开始提取元素...")
	for i, xpath := range xpaths {
		log.Printf("正在提取第 %d 个XPath: %s", i+1, xpath)
		var elementText string
		var elementHTML string
		err = chromedp.Run(xe.ctx,
			chromedp.Text(xpath, &elementText, chromedp.BySearch),
		)
		if err != nil {
			log.Printf("获取第 %d 个XPath的文本内容失败: %v", i+1, err)
			err = chromedp.Run(xe.ctx,
				chromedp.InnerHTML(xpath, &elementHTML, chromedp.BySearch),
			)
			if err != nil {
				log.Printf("获取第 %d 个XPath的HTML内容也失败: %v", i+1, err)
				var jsResult string
				err = chromedp.Run(xe.ctx,
					chromedp.Evaluate(fmt.Sprintf(`
						function getElementByXPath(xpath) {
							try {
								const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
								const element = result.singleNodeValue;
								if (element) {
									return {
										text: element.textContent || element.innerText || '',
										html: element.innerHTML || '',
										outerHTML: element.outerHTML || '',
										tagName: element.tagName || '',
										attributes: Array.from(element.attributes || []).map(attr => ({
											name: attr.name,
											value: attr.value
										}))
									};
								} else {
									return null;
								}
							} catch (e) {
								return { error: e.message };
							}
						}
						
						JSON.stringify(getElementByXPath('%s'));
					`, xpath), &jsResult),
				)
				if err != nil {
					fmt.Printf("❌ XPath %d: 无法获取元素 - %v\n", i+1, err)
				} else {
					fmt.Printf("📍 XPath %d 的JavaScript查询结果:\n%s\n", i+1, jsResult)
				}
			} else {
				fmt.Printf("✅ XPath %d (HTML内容):\n%s\n", i+1, elementHTML)
			}
		} else {
			fmt.Printf("✅ XPath %d (文本内容):\n%s\n", i+1, elementText)
		}
		fmt.Println("---")
	}
	return nil
}

func (xe *XPathExtractor) ExtractWithWait(url string, xpaths []string) ([]string, error) {
	log.Printf("正在访问网页: %s", url)
	err := chromedp.Run(xe.ctx,
		chromedp.Navigate(url),
		chromedp.WaitVisible(`body`, chromedp.ByQuery),
	)
	if err != nil {
		return nil, fmt.Errorf("访问网页失败: %v", err)
	}
	log.Printf("页面加载完成，开始提取元素...")
	var jsResult string
	xpathsJSON := "["
	for i, xpath := range xpaths {
		if i > 0 {
			xpathsJSON += ","
		}
		xpathsJSON += fmt.Sprintf(`"%s"`, xpath)
	}
	xpathsJSON += "]"
	err = chromedp.Run(xe.ctx,
		chromedp.Evaluate(fmt.Sprintf(`
			function extractMultipleXPaths(xpaths) {
				const results = [];
				
				xpaths.forEach((xpath, index) => {
					try {
						const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
						const element = result.singleNodeValue;
						
						if (element) {
							results.push({
								index: index + 1,
								xpath: xpath,
								success: true,
								text: element.textContent ? element.textContent.trim() : '',
								innerText: element.innerText ? element.innerText.trim() : '',
								innerHTML: element.innerHTML || '',
								tagName: element.tagName || '',
								className: element.className || '',
								id: element.id || '',
								attributes: Array.from(element.attributes || []).map(attr => ({
									name: attr.name,
									value: attr.value
								}))
							});
						} else {
							results.push({
								index: index + 1,
								xpath: xpath,
								success: false,
								error: 'Element not found'
							});
						}
					} catch (e) {
						results.push({
							index: index + 1,
							xpath: xpath,
							success: false,
							error: e.message
						});
					}
				});
				
				return results;
			}
			
			JSON.stringify(extractMultipleXPaths(%s), null, 2);
		`, xpathsJSON), &jsResult),
	)

	if err != nil {
		return nil, fmt.Errorf("执行JavaScript提取失败: %v", err)
	}
	var results []map[string]interface{}
	err = json.Unmarshal([]byte(jsResult), &results)
	if err != nil {
		return nil, fmt.Errorf("解析JavaScript结果失败: %v", err)
	}
	var textResults []string
	for _, result := range results {
		if success, ok := result["success"].(bool); ok && success {
			if text, ok := result["text"].(string); ok {
				textResults = append(textResults, text)
			} else {
				textResults = append(textResults, "")
			}
		} else {
			textResults = append(textResults, "")
		}
	}
	fmt.Println("🔍 XPath提取结果:")
	fmt.Println("==================")
	for i, text := range textResults {
		fmt.Printf("XPath %d: %s\n", i+1, text)
	}
	return textResults, nil
}

func (xe *XPathExtractor) ExtractTexts(url string, xpaths []string) ([]string, error) {
	return xe.ExtractWithWait(url, xpaths)
}

package _6kr

type Investor struct {
	ID            int64    `json:"id"`
	Logo          string   `json:"logoUrl"`
	Name          string   `json:"name"`
	ProjectName   string   `json:"projectName"`
	EventTotal    string   `json:"eventTotal"`
	ManCapl       string   `json:"manCapl"`
	Type          []string `json:"type"`
	Area          string   `json:"area"`
	EstablishTime string   `json:"establishTime"`
}

type (
	RequestParam struct {
		PageNo     string   `json:"pageNo"`
		PageSize   string   `json:"pageSize"`
		TypeIdList []string `json:"typeIdList"`
		Keyword    string   `json:"keyword"`
		SiteId     int      `json:"siteId"`
		PlatformId int      `json:"platformId"`
	}
	Request struct {
		PartnerID      string       `json:"partner_id"`
		Timestamp      int64        `json:"timestamp"`
		PartnerVersion string       `json:"partner_version"`
		Param          RequestParam `json:"param"`
	}
	ResponseDataPage struct {
		PrePage    int `json:"prePage"`
		NextPage   int `json:"nextPage"`
		PageNo     int `json:"pageNo"`
		PageSize   int `json:"pageSize"`
		TotalPage  int `json:"totalPage"`
		TotalCount int `json:"totalCount"`
	}
	ResponseData struct {
		InvestorList []Investor        `json:"investorList"`
		Page         *ResponseDataPage `json:"page"`
	}
	Response struct {
		Code int           `json:"code"`
		Data *ResponseData `json:"data"`
	}
)
